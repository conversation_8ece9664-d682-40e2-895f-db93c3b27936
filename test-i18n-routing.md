# 国际化路由测试指南

## 测试目标
验证默认英文路径不显示 `/en` 标识，其他语言显示对应标识的功能。

## 预期行为

### 英文（默认语言）
- 访问 `/` → 显示英文内容，URL 保持为 `/`
- 访问 `/about` → 显示英文内容，URL 保持为 `/about`
- 访问 `/en` → 重定向到 `/`（301重定向）
- 访问 `/en/about` → 重定向到 `/about`（301重定向）

### 中文
- 访问 `/zh` → 显示中文内容，URL 保持为 `/zh`
- 访问 `/zh/about` → 显示中文内容，URL 保持为 `/zh/about`

### 语言切换
- 在英文页面切换到中文 → 跳转到 `/zh`
- 在中文页面切换到英文 → 跳转到 `/`
- 在英文子页面切换到中文 → 跳转到 `/zh/子页面`
- 在中文子页面切换到英文 → 跳转到 `/子页面`

## 测试步骤

1. **启动开发服务器**
   ```bash
   pnpm dev
   ```

2. **测试默认语言路径**
   - 访问 `http://localhost:3000/`
   - 确认显示英文内容，URL 不包含 `/en`
   - 访问 `http://localhost:3000/en`
   - 确认重定向到 `http://localhost:3000/`

3. **测试非默认语言路径**
   - 访问 `http://localhost:3000/zh`
   - 确认显示中文内容，URL 包含 `/zh`

4. **测试语言切换器**
   - 在首页点击语言切换器
   - 从英文切换到中文，确认跳转到 `/zh`
   - 从中文切换到英文，确认跳转到 `/`

5. **测试导航链接**
   - 确认所有内部链接正确生成
   - 英文环境下链接不包含 `/en` 前缀
   - 中文环境下链接包含 `/zh` 前缀

## 关键文件修改

1. **src/i18n/routing.ts**
   - 将 `localePrefix` 从 `"always"` 改为 `"as-needed"`

2. **src/middleware.ts**
   - 添加默认语言前缀重定向逻辑
   - 更新 auth 路径重定向逻辑

3. **src/components/sections/LandingHeader.tsx**
   - 使用 next-intl 的 useRouter 和 useLocale
   - 更新语言切换逻辑
   - 修复硬编码的链接路径

## 注意事项

- 确保所有组件使用 next-intl 的 Link 组件而不是 Next.js 的原生 Link
- 语言切换器应该使用 router.replace 而不是 window.location.href
- 所有内部链接应该让 next-intl 自动处理语言前缀
